import type { GameService } from '../../services/gameService';
import { logger } from '../../utils/logger';
import type { Server, Socket } from 'socket.io';
import type { GameState, GameAction } from '../../types/game';

// Game configuration constants (mirrored from client)
const GAME_CONFIG = {
  GRID_SIZE: 4,
  INITIAL_ACTIVE_BLOCKS: 3,
  GAME_DURATION: 30000, // 30 seconds in milliseconds
  COOLDOWN_DURATION: 500, // 0.5 seconds in milliseconds
  SCORE_TIERS: {
    FAST: 5,        // < 500ms
    MEDIUM_FAST: 4, // < 1000ms
    MEDIUM: 3,      // < 1500ms
    MEDIUM_SLOW: 2, // < 2000ms
    SLOW: 1         // >= 2000ms
  },
  SCORE_TIER_THRESHOLDS: {
    FAST: 500,
    MEDIUM_FAST: 1000,
    MEDIUM: 1500,
    MEDIUM_SLOW: 2000
  },
  WRONG_CLICK_PENALTY: 5,
  MAX_LIVES: 3
};

interface GridBlock {
  id: string;
  row: number;
  col: number;
  isActive: boolean;
  index: number;
}

interface FingerFrenzyGameData {
  grid: GridBlock[];
  activeBlockCount: number;
}

export class FingerFrenzyController {
  private gameService: GameService;
  private gameData: Map<string, FingerFrenzyGameData> = new Map();

  constructor(gameService: GameService) {
    this.gameService = gameService;
  }

  /**
   * Initialize and start a new Finger Frenzy game session
   */
  initializeAndStartGame(roomId: string, playerId: string): { success: boolean; gameState?: GameState; message?: string } {
    // Check if room already has a game state
    let gameState = this.gameService.getGameState(roomId);

    if (gameState) {
      // Game state exists, check if we can start
      if (gameState.status === 'active') {
        return { success: false, message: 'Game is already active' };
      }
      if (gameState.status === 'ended') {
        return { success: false, message: 'Game session has ended - no restarts allowed' };
      }
    } else {
      // Create new game state
      gameState = this.gameService.createGameState(roomId, 'finger-frenzy', playerId, GAME_CONFIG.MAX_LIVES);
    }

    // Start the game using GameService
    const startSuccess = this.gameService.startGame(roomId);
    if (!startSuccess) {
      return { success: false, message: 'Failed to start game' };
    }

    // Create and initialize game data with grid
    const grid = this.createGrid();
    const gameData: FingerFrenzyGameData = {
      grid,
      activeBlockCount: 0
    };

    this.gameData.set(roomId, gameData);

    // Activate initial blocks for game start
    this.activateInitialBlocks(roomId);

    logger.info(`Finger Frenzy game initialized and started for room ${roomId}, player ${playerId}`);

    return { success: true, gameState };
  }

  /**
   * End the game session
   */
  endGame(roomId: string, reason: 'timeout' | 'no_lives' | 'manual' = 'manual'): boolean {
    const gameState = this.gameService.getGameState(roomId);
    if (!gameState || gameState.status !== 'active') {
      logger.warn(`Cannot end game: no active game in room ${roomId}`);
      return false;
    }

    // End game using GameService
    const endReason = reason === 'timeout' ? 'timeout' : reason === 'no_lives' ? 'completed' : 'forfeit';
    this.gameService.endGame(roomId, endReason);

    // Deactivate all blocks
    this.deactivateAllBlocks(roomId);

    // Clean up game state
    this.cleanupGame(roomId);

    logger.info(`Finger Frenzy game ended in room ${roomId}, reason: ${reason}, final score: ${gameState.score}`);
    return true;
  }

  /**
   * Create a 4x4 grid of blocks
   */
  createGrid(): GridBlock[] {
    const grid: GridBlock[] = [];

    for (let row = 0; row < GAME_CONFIG.GRID_SIZE; row++) {
      for (let col = 0; col < GAME_CONFIG.GRID_SIZE; col++) {
        const index = row * GAME_CONFIG.GRID_SIZE + col;
        const id = `block_${row}_${col}`;
        grid.push({
          id,
          row,
          col,
          isActive: false,
          index
        });
      }
    }

    return grid;
  }

  /**
   * Activate initial blocks when game starts
   */
  activateInitialBlocks(roomId: string): void {
    const gameData = this.gameData.get(roomId);
    if (!gameData) {
      return;
    }

    // Activate 3 specific initial positions (matching client logic)
    const initialPositions = [
      { row: 1, col: 2 },
      { row: 2, col: 3 },
      { row: 0, col: 1 }
    ];

    gameData.activeBlockCount = 0;

    for (const pos of initialPositions) {
      const index = pos.row * GAME_CONFIG.GRID_SIZE + pos.col;
      if (index < gameData.grid.length) {
        gameData.grid[index].isActive = true;
        gameData.activeBlockCount++;
      }
    }

    logger.info(`Activated ${gameData.activeBlockCount} initial blocks for room ${roomId}`);
  }

  /**
   * Handle a tile tap by ID
   */
  handleTileTap(roomId: string, tileId: string, reactionTime: number): {
    success: boolean;
    isCorrect: boolean;
    points: number;
    penalty: number;
    newScore: number;
    newLives: number;
    newBlock?: GridBlock | null;
    gameEnded: boolean;
    message?: string;
  } {
    const gameState = this.gameService.getGameState(roomId);
    const gameData = this.gameData.get(roomId);

    if (!gameState || !gameData) {
      return {
        newBlock: null,
        success: false,
        isCorrect: false,
        points: 0,
        penalty: 0,
        newScore: 0,
        newLives: 0,
        gameEnded: false,
        message: 'Game state not found'
      };
    }

    if (gameState.status !== 'active') {
      return {
        newBlock: null,
        success: false,
        isCorrect: false,
        points: 0,
        penalty: 0,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        message: 'Game is not active'
      };
    }

    // Find the block by ID
    const block = gameData.grid.find(b => b.id === tileId);
    if (!block) {
      return {
        newBlock: null,
        success: false,
        isCorrect: false,
        points: 0,
        penalty: 0,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        message: 'Invalid tile ID'
      };
    }

    // Check if the tile is active (correct tap) or inactive (wrong tap)
    if (block.isActive) {
      // CORRECT TAP
      // Validate reaction time (should be reasonable)
      if (reactionTime < 0 || reactionTime > 10000) {
        return {
          newBlock: null,
          success: false,
          isCorrect: false,
          points: 0,
          penalty: 0,
          newScore: gameState.score,
          newLives: gameState.lives,
          gameEnded: false,
          message: 'Invalid reaction time'
        };
      }

      // Calculate points based on reaction time
      const points = this.calculatePoints(reactionTime);

      // Update score using GameService
      const newScore = gameState.score + points;
      this.gameService.updateScore(roomId, gameState.player, newScore);

      // Deactivate the clicked block
      block.isActive = false;
      gameData.activeBlockCount--;

      // Activate a new random block (excluding the clicked one)
      const newBlock = this.activateRandomBlock(roomId, block.index);

      logger.info(`Correct tap in room ${roomId}: tile ${tileId}, reaction time ${reactionTime}ms, points ${points}, new score ${newScore}`);

      return {
        newBlock,
        success: true,
        isCorrect: true,
        points,
        penalty: 0,
        newScore,
        newLives: gameState.lives,
        gameEnded: false,
      };
    } else {
      // WRONG TAP
      // Apply penalty
      const penalty = GAME_CONFIG.WRONG_CLICK_PENALTY;
      const newScore = Math.max(0, gameState.score - penalty);
      this.gameService.updateScore(roomId, gameState.player, newScore);

      // Deduct life using GameService
      const livesResult = this.gameService.deductLife(roomId, gameState.player);

      // Check if game should end due to no lives
      const gameEnded = livesResult.gameEnded;
      if (gameEnded) {
        this.endGame(roomId, 'no_lives');
      }

      logger.info(`Wrong tap in room ${roomId}: tile ${tileId}, penalty ${penalty}, new score ${newScore}, lives ${livesResult.newLives}, game ended: ${gameEnded}`);

      return {
        newBlock: null,
        success: true,
        isCorrect: false,
        points: 0,
        penalty,
        newScore,
        newLives: livesResult.newLives,
        gameEnded
      };
    }
  }

  /**
   * Get all tile states for the room
   */
  getAllTileStates(roomId: string): { [tileId: string]: { isActive: boolean; } } | null {
    const gameData = this.gameData.get(roomId);
    if (!gameData) {
      return null;
    }

    const tileStates: { [tileId: string]: { isActive: boolean; } } = {};

    gameData.grid.forEach(block => {
      tileStates[block.id] = {
        isActive: block.isActive,
      };
    });

    return tileStates;
  }

  /**
   * Calculate points based on reaction time
   */
  calculatePoints(reactionTime: number): number {
    if (reactionTime < GAME_CONFIG.SCORE_TIER_THRESHOLDS.FAST) {
      return GAME_CONFIG.SCORE_TIERS.FAST;
    } else if (reactionTime < GAME_CONFIG.SCORE_TIER_THRESHOLDS.MEDIUM_FAST) {
      return GAME_CONFIG.SCORE_TIERS.MEDIUM_FAST;
    } else if (reactionTime < GAME_CONFIG.SCORE_TIER_THRESHOLDS.MEDIUM) {
      return GAME_CONFIG.SCORE_TIERS.MEDIUM;
    } else if (reactionTime < GAME_CONFIG.SCORE_TIER_THRESHOLDS.MEDIUM_SLOW) {
      return GAME_CONFIG.SCORE_TIERS.MEDIUM_SLOW;
    } else {
      return GAME_CONFIG.SCORE_TIERS.SLOW;
    }
  }

  /**
   * Activate a random block (excluding specified block and already active blocks)
   */
  activateRandomBlock(roomId: string, excludeBlockIndex?: number): GridBlock | null {
    const gameState = this.gameService.getGameState(roomId);
    const gameData = this.gameData.get(roomId);

    if (!gameState || !gameData || gameState.status !== 'active') {
      return null;
    }

    // Don't activate more blocks if we already have the maximum
    if (gameData.activeBlockCount >= GAME_CONFIG.INITIAL_ACTIVE_BLOCKS) {
      return null;
    }

    // Get list of available positions (exclude currently active blocks and the excluded block)
    const availableIndices: number[] = [];

    gameData.grid.forEach((block: GridBlock, index: number) => {
      const isExcluded = excludeBlockIndex !== undefined && index === excludeBlockIndex;

      if (!block.isActive && !isExcluded) {
        availableIndices.push(index);
      }
    });

    // If we have available positions, activate one randomly
    if (availableIndices.length > 0) {
      const randomIndex = availableIndices[Math.floor(Math.random() * availableIndices.length)];
      const block = gameData.grid[randomIndex];

      block.isActive = true;
      gameData.activeBlockCount++;

      logger.info(`Activated random block ${randomIndex} in room ${roomId}, active count: ${gameData.activeBlockCount}`);
      return block;
    }

    return null;
  }

  /**
   * Get grid state for client synchronization
   */
  getGridState(roomId: string): { blocks: GridBlock[], activeCount: number } | null {
    const gameData = this.gameData.get(roomId);
    if (!gameData) {
      return null;
    }

    return {
      blocks: gameData.grid.map((block: GridBlock) => ({ ...block })), // Return a copy
      activeCount: gameData.activeBlockCount
    };
  }

  /**
   * Deactivate all blocks (useful for game end or reset)
   */
  deactivateAllBlocks(roomId: string): boolean {
    const gameData = this.gameData.get(roomId);
    if (!gameData) {
      return false;
    }

    gameData.grid.forEach((block: GridBlock) => {
      block.isActive = false;
    });
    gameData.activeBlockCount = 0;

    logger.info(`Deactivated all blocks in room ${roomId}`);
    return true;
  }

  /**
   * Clean up game state when room is destroyed
   */
  cleanupGame(roomId: string): boolean {
    const deleted = this.gameService.deleteGameState(roomId);
    this.gameData.delete(roomId);

    if (deleted) {
      logger.info(`Cleaned up Finger Frenzy game for room ${roomId}`);
    }
    return deleted;
  }

  /**
   * Setup socket event handlers for Finger Frenzy
   */
  public setupSocketHandlers(io: Server, socket: Socket): void {
    // Generic game start event
    socket.on('start', (data) => {
      if (data.gameId === 'finger-frenzy') {
        this.handleGameStart(io, socket, data);
      }
    });

    // Generic game end event
    socket.on('end', (data) => {
      if (data.gameId === 'finger-frenzy') {
        this.handleGameEnd(io, socket, data);
      }
    });

    // Generic game action event (for game-specific actions)
    socket.on('action', (data) => {
      if (data.gameId === 'finger-frenzy') {
        this.handleGameAction(io, socket, data);
      }
    });
  }

  /**
   * Handle generic game action event
   */
  handleGameAction(io: Server, socket: Socket, data: any): void {
    const { roomId, playerId, gameId, action } = data;

    if (!roomId || !playerId || !gameId || !action) {
      socket.emit('error', {
        message: 'Missing required data for game action (roomId, playerId, gameId, action)'
      });
      return;
    }

    try {
      // Process the game action using GameService
      const gameAction: GameAction = {
        type: action.type,
        data: action.data,
        timestamp: Date.now()
      };

      const success = this.gameService.processGameAction(roomId, playerId, gameAction);

      if (success) {
        // Handle specific action types for Finger Frenzy
        switch (action.type) {
          case 'tile_tap':
            this.handleTileTapAction(io, socket, data);
            break;
          default:
            socket.emit('error', {
              message: `Unknown action type: ${action.type}`
            });
        }
      } else {
        socket.emit('error', {
          message: 'Failed to process game action'
        });
      }
    } catch (error) {
      logger.error(`Error processing game action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game start event
   */
  handleGameStart(io: Server, socket: Socket, data: any): void {
    const { roomId, playerId, gameId } = data;

    if (!roomId || !playerId || !gameId) {
      socket.emit('game_error', {
        message: 'Missing roomId, playerId, or gameId'
      });
      return;
    }

    try {
      // Initialize and start the game in one step
      const result = this.initializeAndStartGame(roomId, playerId);

      if (result.success && result.gameState) {
        // Start timer using GameService
        this.gameService.setGameTimer(roomId, GAME_CONFIG.GAME_DURATION, () => {
          this.endGame(roomId, 'timeout');
          io.to(roomId).emit('ended', {
            reason: 'timeout',
            finalScore: result.gameState!.score,
          });
        });

        // Get initial grid state
        const gridState = this.getGridState(roomId);

        // Broadcast game start to all players in room
        io.to(roomId).emit('started', {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime
          },
          gridState,
          message: 'Game started!'
        });

        logger.info(`${gameId} game started in room ${roomId} by player ${playerId}`);
      } else {
        socket.emit('error', {
          message: result.message || 'Failed to start game'
        });
      }
    } catch (error) {
      logger.error(`Error starting ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle generic game end event
   */
  handleGameEnd(io: Server, socket: Socket, data: any): void {
    const { roomId, playerId, reason = 'manual', gameId } = data;

    if (!roomId || !playerId || !gameId) {
      socket.emit('error', {
        message: 'Missing roomId, playerId, or gameId'
      });
      return;
    }

    try {
      const gameState = this.gameService.getGameState(roomId);
      if (!gameState) {
        socket.emit('error', {
          message: 'Game state not found'
        });
        return;
      }

      const success = this.endGame(roomId, reason);

      if (success) {
        // Broadcast game end to all players in room
        io.to(roomId).emit('ended', {
          reason,
          finalScore: gameState.score,
        });

        logger.info(`${gameId} game ended in room ${roomId} by player ${playerId}, reason: ${reason}`);
      } else {
        socket.emit('error', {
          message: 'Failed to end game'
        });
      }
    } catch (error) {
      logger.error(`Error ending ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle tile tap action
   */
  handleTileTapAction(io: Server, socket: Socket, data: any): void {
    const { roomId, playerId, gameId, action } = data;
    const { tileId, reactionTime, clickTime } = action.data;

    if (!roomId || !playerId || !tileId || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for tile tap action'
      });
      return;
    }

    try {
      // Validate the tap timing
      const currentTime = Date.now();
      const timeDiff = Math.abs(currentTime - (clickTime || currentTime));
      if (timeDiff > 5000) { // 5 seconds tolerance
        socket.emit('error', {
          message: 'Tap time too far from current time'
        });
        return;
      }

      // Process the tile tap
      const result = this.handleTileTap(roomId, tileId, reactionTime || 0);

      if (result.success) {
        // Get updated grid state and tile states
        const gridState = this.getGridState(roomId);
        const tileStates = this.getAllTileStates(roomId);
        const gameState = this.gameService.getGameState(roomId);

        // Broadcast the action result to all players in room
        io.to(roomId).emit('action_result', {
          actionType: 'tile_tap',
          data: {
            tileId,
            isCorrect: result.isCorrect,
            points: result.points,
            penalty: result.penalty,
            newScore: result.newScore,
            newLives: result.newLives,
            gameEnded: result.gameEnded,
            reactionTime: reactionTime || 0,
            gridState,
            tileStates,
            gameState: gameState ? {
              score: gameState.score,
              lives: gameState.lives,
              isActive: gameState.status === 'active'
            } : null
          }
        });

        // If game ended, broadcast game end event
        if (result.gameEnded) {
          io.to(roomId).emit('ended', {
            reason: 'no_lives',
            finalScore: result.newScore
          });
        }

        logger.info(`Tile tap processed for room ${roomId}, player ${playerId}, tile ${tileId}, correct: ${result.isCorrect}, points: ${result.points}, penalty: ${result.penalty}`);
      } else {
        socket.emit('game_error', {
          message: result.message || 'Failed to process tile tap'
        });
      }
    } catch (error) {
      logger.error(`Error processing tile tap in room ${roomId}:`, error);
      socket.emit('game_error', {
        message: 'Internal server error'
      });
    }
  }
}