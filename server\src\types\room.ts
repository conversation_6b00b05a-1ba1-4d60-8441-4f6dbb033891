import { GameType, GameState } from './game';

export interface Room {
  id: string;
  gameType: GameType;
  maxPlayers: number;
  currentPlayers: number;
  players: Player[];
  status: 'waiting' | 'starting' | 'active' | 'ended';
  gameState?: GameState;
  createdAt: number;
  updatedAt: number;
}

export interface Player {
  id: string;
  socketId: string;
  name?: string;
  ready: boolean;
  connected: boolean;
  joinedAt: number;
  score?: number;
}

export interface RoomConfig {
  gameType: GameType;
  maxPlayers: number;
  isPrivate: boolean;
  password?: string;
  timeLimit?: number;
  customRules?: Record<string, any>;
}
