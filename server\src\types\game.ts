export type GameType = 'finger-frenzy' | 'bingo' | 'matching-mayhem' | 'number-sequence';

export interface GameAction {
  type: string;
  data: any;
  timestamp: number;
}

export interface GameScore {
  playerId: string;
  score: number;
  gameType: GameType;
  timestamp: number;
}

export interface GameState {
  gameType: GameType;
  status: 'waiting' | 'starting' | 'active' | 'paused' | 'ended';
  startTime?: number;
  player: string;
  score: number;
  lives: number;
}

export interface GameResults {
  gameType: GameType;
  score:  number;
  endReason: 'completed'| 'dead' | 'timeout' | 'disconnection' | 'forfeit';
}
