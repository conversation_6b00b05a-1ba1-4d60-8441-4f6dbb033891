import { Room, Player, RoomConfig } from '../types/room';
import { GameType } from '../types/game';
import { logger } from '../utils/logger';
import { MAX_PLAYERS_PER_ROOM } from '../utils/constants';

class RoomService {
  private rooms: Map<string, Room> = new Map();

  createRoom(config: RoomConfig): Room {
    const roomId = this.generateRoomId();
    const room: Room = {
      id: roomId,
      gameType: config.gameType,
      maxPlayers: config.maxPlayers || MAX_PLAYERS_PER_ROOM[config.gameType],
      currentPlayers: 0,
      players: [],
      status: 'waiting',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    this.rooms.set(roomId, room);
    logger.info(`Room created: ${roomId} for game ${config.gameType}`);
    return room;
  }

  getRoom(roomId: string): Room | undefined {
    return this.rooms.get(roomId);
  }

  getAllRooms(): Room[] {
    return Array.from(this.rooms.values());
  }

  getRoomsByGameType(gameType: GameType): Room[] {
    return Array.from(this.rooms.values()).filter(room => room.gameType === gameType);
  }

  addPlayerToRoom(roomId: string, player: Player): boolean {
    const room = this.rooms.get(roomId);
    if (!room) {
      logger.warn(`Attempted to add player to non-existent room: ${roomId}`);
      return false;
    }

    if (room.currentPlayers >= room.maxPlayers) {
      logger.warn(`Room ${roomId} is full, cannot add player ${player.id}`);
      return false;
    }

    // Check if player is already in room
    const existingPlayer = room.players.find(p => p.id === player.id);
    if (existingPlayer) {
      logger.warn(`Player ${player.id} already in room ${roomId}`);
      return false;
    }

    room.players.push(player);
    room.currentPlayers++;
    room.updatedAt = Date.now();

    logger.info(`Player ${player.id} added to room ${roomId}`);
    return true;
  }

  removePlayerFromRoom(roomId: string, playerId: string): boolean {
    const room = this.rooms.get(roomId);
    if (!room) {
      logger.warn(`Attempted to remove player from non-existent room: ${roomId}`);
      return false;
    }

    const playerIndex = room.players.findIndex(p => p.id === playerId);
    if (playerIndex === -1) {
      logger.warn(`Player ${playerId} not found in room ${roomId}`);
      return false;
    }

    room.players.splice(playerIndex, 1);
    room.currentPlayers--;
    room.updatedAt = Date.now();

    // If room is empty, consider removing it
    if (room.currentPlayers === 0) {
      this.scheduleRoomCleanup(roomId);
    }

    logger.info(`Player ${playerId} removed from room ${roomId}`);
    return true;
  }

  updatePlayerReady(roomId: string, playerId: string, ready: boolean): boolean {
    const room = this.rooms.get(roomId);
    if (!room) return false;

    const player = room.players.find(p => p.id === playerId);
    if (!player) return false;

    player.ready = ready;
    room.updatedAt = Date.now();

    logger.info(`Player ${playerId} ready status updated to ${ready} in room ${roomId}`);
    return true;
  }

  areAllPlayersReady(roomId: string): boolean {
    const room = this.rooms.get(roomId);
    if (!room || room.players.length === 0) return false;

    return room.players.every(player => player.ready);
  }

  deleteRoom(roomId: string): boolean {
    const deleted = this.rooms.delete(roomId);
    if (deleted) {
      logger.info(`Room ${roomId} deleted`);
    }
    return deleted;
  }

  private generateRoomId(): string {
    // Generate a simple room ID (in production, use a more robust method)
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  private scheduleRoomCleanup(roomId: string): void {
    // Schedule room cleanup after 5 minutes of being empty
    setTimeout(() => {
      const room = this.rooms.get(roomId);
      if (room && room.currentPlayers === 0) {
        this.deleteRoom(roomId);
      }
    }, 5 * 60 * 1000); // 5 minutes
  }

  // Cleanup inactive rooms
  cleanupInactiveRooms(): void {
    const now = Date.now();
    const maxIdleTime = 30 * 60 * 1000; // 30 minutes

    for (const [roomId, room] of this.rooms.entries()) {
      if (now - room.updatedAt > maxIdleTime) {
        logger.info(`Cleaning up inactive room: ${roomId}`);
        this.deleteRoom(roomId);
      }
    }
  }
}

export const roomService = new RoomService();
export default roomService;
