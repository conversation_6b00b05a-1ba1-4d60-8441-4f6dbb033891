import gameService from "../../services/gameService";
import type { GameService } from "../../services/gameService";

class ScoreManager {
    private gameService: GameService;

    constructor() {
        this.gameService = gameService;
    }

    public addPoints( roomId: string, points: number): void {
        const gameState = this.gameService.getGameState(roomId);
        if (!gameState) {
            return;
        }

        this.gameService.updateScore(roomId, gameState.player, gameState.score + points);
    }

    public subtractPoints( roomId: string, points: number): void {
        const gameState = this.gameService.getGameState(roomId);
        if (!gameState) {
            return;
        }

        let newScore = gameState.score - points;
        if (newScore < 0) {
            newScore = 0;
        }

        this.gameService.updateScore(roomId, gameState.player, newScore);
    }
}

export default ScoreManager;