export const GAME_TYPES = {
  FINGER_FRENZY: 'finger-frenzy',
  BINGO: 'bingo',
  MATCHING_MAYHEM: 'matching-mayhem',
  NUMBER_SEQUENCE: 'number-sequence'
} as const;

export const ROOM_STATUS = {
  WAITING: 'waiting',
  STARTING: 'starting',
  ACTIVE: 'active',
  ENDED: 'ended'
} as const;

export const GAME_STATUS = {
  WAITING: 'waiting',
  STARTING: 'starting',
  ACTIVE: 'active',
  PAUSED: 'paused',
  ENDED: 'ended'
} as const;

export const MAX_PLAYERS_PER_ROOM = {
  [GAME_TYPES.FINGER_FRENZY]: 4,
  [GAME_TYPES.BINGO]: 6,
  [GAME_TYPES.MATCHING_MAYHEM]: 4,
  [GAME_TYPES.NUMBER_SEQUENCE]: 4
} as const;

export const DEFAULT_GAME_DURATION = {
  [GAME_TYPES.FINGER_FRENZY]: 60000, // 1 minute
  [GAME_TYPES.BINGO]: 300000, // 5 minutes
  [GAME_TYPES.MATCHING_MAYHEM]: 180000, // 3 minutes
  [GAME_TYPES.NUMBER_SEQUENCE]: 120000 // 2 minutes
} as const;

export const SOCKET_EVENTS = {
  // Connection
  CONNECTION: 'connection',
  DISCONNECT: 'disconnect',
  
  // Room events
  JOIN_ROOM: 'join_room',
  LEAVE_ROOM: 'leave_room',
  ROOM_JOINED: 'room_joined',
  ROOM_LEFT: 'room_left',
  PLAYER_JOINED: 'player_joined',
  PLAYER_LEFT: 'player_left',
  
  // Game events
  PLAYER_READY: 'player_ready',
  PLAYER_READY_UPDATE: 'player_ready_update',
  GAME_ACTION: 'game_action',
  OPPONENT_ACTION: 'opponent_action',
  START_GAME: 'start_game',
  END_GAME: 'end_game',
  GAME_START: 'game_start',
  GAME_END: 'game_end',
  GAME_STATE_UPDATE: 'game_state_update',
  
  // Score events
  SUBMIT_SCORE: 'submit_score',
  SCORE_UPDATE: 'score_update',
  
  // General
  MESSAGE: 'message',
  ERROR: 'error'
} as const;
