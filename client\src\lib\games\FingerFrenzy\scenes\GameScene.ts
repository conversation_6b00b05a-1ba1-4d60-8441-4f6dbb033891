import Phaser from 'phaser';
import ScoreManager from '../managers/ScoreManager';
import TimerManager from '../managers/TimerManager';
import LivesManager from '../managers/LivesManager';
import TimerBarUI from '../ui/TimerBarUI';
import GridBlock from '../objects/GridBlock';
import GameConfig from '../config/GameConfig';
import TicTapsConnector from '../utils/TicTapsConnector';
import { gameActions } from '$lib/stores';
import type { SocketClient } from '$lib/socket';
import type { FingerFrenzyConfig } from '../index';

export default class GameScene extends Phaser.Scene {
  private blocks: GridBlock[] = [];

  private gameEnd: boolean = false;

  // UI Elements
  private UIContainer!: Phaser.GameObjects.Container;
  private gridContainer!: Phaser.GameObjects.Container;

  private scoreManager!: ScoreManager;
  private timerManager!: TimerManager;
  private livesManager!: LivesManager;
  private timerBarUI!: TimerBarUI;

  // TicTaps integration
  private ticTaps: TicTapsConnector;
  private socketClient: SocketClient | null = null;

  // Game session data
  private roomId: string = 'default-room';
  private playerId: string = 'player-1';
  private gameId: string = 'finger-frenzy';

  constructor() {
    super({ key: 'GameScene' });
    this.ticTaps = new TicTapsConnector();
  }

  init(): void {
    // Reset game state
    this.gameEnd = false;

    // Get socket client from game registry
    const gameConfig = this.registry.get('gameConfig') as FingerFrenzyConfig;
    this.socketClient = gameConfig?.socketClient || null;

    // Setup socket event listeners
    this.setupSocketEventListeners();

    // Initialize managers
    this.scoreManager = new ScoreManager(this, {
      initialScore: 0,
      fontSize: '80px',
      scoreColor: '#33DDFF'
    });

    this.timerManager = new TimerManager(this, {
      duration: GameConfig.GAME_DURATION,
      warningThreshold: 5
    });

    this.livesManager = new LivesManager(this, {
      initialLives: 3
    });

    this.timerBarUI = new TimerBarUI(this, {
      width: this.cameras.main.width * 0.8,
      height: 35,
      x: this.cameras.main.width / 2,
      y: this.cameras.main.height * 0.07
    });

    // Set up timer events
    this.timerManager.on('timeUp', () => this.endGame());
    this.timerManager.on('timerUpdate', (state) => {
      this.timerBarUI.updateProgress(state.progress);
    });

    this.livesManager.on('heartDeducted', (lives) => {
      if (lives === 0) {
        this.endGame();
      }
    });
  }

  create(): void {
    const { width, height } = this.cameras.main;

    // Simple gradient background
    this.createBackground();
    
    // Start countdown immediately
    this.startCountdown();
  }

  shutdown(): void {
    this.tweens.killAll();

    // Clean up socket event listeners
    this.cleanupSocketEventListeners();

    // Clean up managers
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }

    if (this.timerManager) {
      this.timerManager.destroy();
    }

    if (this.timerBarUI) {
      this.timerBarUI.destroy();
    }

    if (this.livesManager) {
      this.livesManager.destroy();
    }
  }

  /**
   * Clean up socket event listeners
   */
  private cleanupSocketEventListeners(): void {
    if (!this.socketClient) return;

    // Remove custom event listeners to prevent memory leaks
    // Note: We would need to store references to the callback functions to properly remove them
    // For now, we'll just log the cleanup
    console.log('Cleaning up socket event listeners for FingerFrenzy GameScene');
  }

  /**
   * Setup socket event listeners for server communication
   */
  private setupSocketEventListeners(): void {
    if (!this.socketClient) return;

    // Add custom event listeners for game-specific handling
    this.socketClient.addCustomEventListener('started', (data: any) => {
      console.log('Game started by server:', data);
      if (data.gameState) {
        this.syncGameState(data.gameState);
      }
      if (data.gridState) {
        this.syncGridState(data.gridState);
      }
    });

    this.socketClient.addCustomEventListener('ended', (data: any) => {
      console.log('Game ended by server:', data);
      this.endGame();
    });

    this.socketClient.addCustomEventListener('score', (data: any) => {
      console.log('Score update from server:', data);
      this.scoreManager.setScore(data.score);
    });

    this.socketClient.addCustomEventListener('deductHeart', (data: any) => {
      console.log('Lives update from server:', data);
      this.livesManager.deductHeart();
    });

    this.socketClient.addCustomEventListener('timer', (data: any) => {
      console.log('Timer update from server:', data);
      this.timerBarUI.updateProgress(data.progress);
    });

    this.socketClient.addCustomEventListener('action_result', (data: any) => {
      console.log('Action result from server:', data);
      if (data.actionType === 'tile_tap') {
        this.handleTileTapResult(data.data);
      }
    });

    this.socketClient.addCustomEventListener('error', (data: any) => {
      console.error('Game error from server:', data);
    });

    this.socketClient.addCustomEventListener('game_error', (data: any) => {
      console.error('Game error from server:', data);
    });

    console.log('Socket event listeners setup for FingerFrenzy GameScene');
  }

  private createBackground(): void {
    const { width, height } = this.cameras.main;
    
    // Create gradient background
    const bgTexture = this.textures.createCanvas('bgTexture', width, height);
    const bgContext = bgTexture?.getContext();
    
    if (bgContext && bgTexture) {
      const gradient = bgContext.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, '#212429');
      gradient.addColorStop(1, '#1C1D22');
      
      bgContext.fillStyle = gradient;
      bgContext.fillRect(0, 0, width, height);
      bgTexture.refresh();
      
      this.add.image(width / 2, height / 2, 'bgTexture').setOrigin(0.5);
    } else {
      // Fallback solid color
      this.cameras.main.setBackgroundColor('#1C1D22');
    }
  }

  private async startCountdown(): Promise<void> {
    const { width, height } = this.cameras.main;

    // Create game UI and start
    this.createUI();
    this.createGrid();
    gameActions.startGame();

    const countdownImages = ['countdown-3', 'countdown-2', 'countdown-1', 'countdown-go'];
    
    // Create countdown overlay
    // const overlay = this.add.rectangle(width / 2, height / 2, width, height, 0x000000, 0.7);
    // const countdownImage = this.add.image(width / 2, height / 2, 'countdown-3').setScale(0);

    // Animate countdown
    for (const texture of countdownImages) {
      // countdownImage.setTexture(texture);
      
      // Play sound
      try {
        this.sound.play(texture === 'countdown-go' ? 'go' : 'countdown');
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      // Animate scale
      // await new Promise<void>((resolve) => {
      //   this.tweens.add({
      //     targets: countdownImage,
      //     scale: 0.2,
      //     duration: 300,
      //     ease: 'Back.easeOut',
      //     onComplete: () => {
      //       this.time.delayedCall(700, () => {
      //         this.tweens.add({
      //           targets: countdownImage,
      //           scale: 0,
      //           duration: 300,
      //           ease: 'Back.easeIn',
      //           onComplete: () => resolve()
      //         });
      //       });
      //     }
      //   });
      // });
      await new Promise<void>((resolve) => {
        this.time.delayedCall(1300, () => resolve());
      });
    }

    // Remove countdown elements
    // overlay.destroy();
    // countdownImage.destroy();

    this.startGame();

    // Record game start time
    this.timerManager.start();
  }

  private createUI(): void {
    const { width, height } = this.cameras.main;

    this.UIContainer = this.add.container(0, 0);
    // this.gamePanel.add(timerContainer);

    // Create timer bar UI
    // this.timerBarUI.create(this.UIContainer);

    // Create timer text at the right circle position
    const rightCirclePos = this.timerBarUI.getRightCirclePosition();
    // this.timerManager.createUI(rightCirclePos.x, rightCirclePos.y, this.UIContainer);

    // Create score display
    const timerBarY = height * 0.07;
    // this.scoreManager.createUI(width / 2, timerBarY + 120, this.UIContainer);

    // Create lives display
    // this.livesManager.createUI(width / 2, timerBarY + 50, this.UIContainer);
  }

  private createGrid(): void {
    const { width, height } = this.cameras.main;
    
    // Container setup
    const containerWidth = Math.min(width * 0.9, 550);
    const containerHeight = containerWidth * 1.2; //1.3
    
    this.gridContainer = this.add.container(width / 2, height * 0.6);
    
    // Background
    const bg = this.add.graphics();
    bg.fillStyle(0x1a1a1a, 1);
    bg.fillRoundedRect(-containerWidth / 2, -containerHeight / 2, containerWidth, containerHeight, 20);
    bg.lineStyle(4, 0x4579F5, 1);
    bg.strokeRoundedRect(-containerWidth / 2, -containerHeight / 2, containerWidth, containerHeight, 20);
    this.gridContainer.add(bg);

    // Create 4x4 grid
    this.blocks = [];
    const gap = containerWidth * 0.03;
    const cellWidth = (containerWidth - (gap * 5)) / 4;
    const cellHeight = (containerHeight - (gap * 5)) / 4;
    const startX = -containerWidth / 2 + gap;
    const startY = -containerHeight / 2 + gap;

    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        const x = startX + col * (cellWidth + gap) + cellWidth / 2;
        const y = startY + row * (cellHeight + gap) + cellHeight / 2;
        
        const block = new GridBlock(this, x, y, cellWidth, cellHeight, row, col);
        this.blocks.push(block);
        this.gridContainer.add(block);
        
        block.on('pointerdown', (pointer: Phaser.Input.Pointer) => this.onBlockClick(block, pointer));
      }
    }
  }

  private onBlockClick(block: GridBlock, pointer: Phaser.Input.Pointer): void {
    if (this.gameEnd || !block) return;
    
    // Check if this is a fresh click (not a held touch)
    // pointer.getDuration() returns how long the pointer has been down
    if (pointer.getDuration() > 100) {
      return; // Ignore held touches
    }
    
    // Disable the block's input immediately to prevent same-touch duplicate events
    block.disableInteractive();

    // Calculate points based on reaction time
    const currentTime = Date.now();
    const reactionTime = currentTime - block.getActivationTime();
    console.log('reactionTime', reactionTime);

    // Send tile tap event to server using proper format
    if (this.socketClient && this.socketClient.isConnected()) {
      const tileId = this.getTileId(block);
      this.socketClient.sendTileTap(this.gameId, this.roomId, this.playerId, tileId, reactionTime);
    }

    const isActive = block.getBlockActive();
    
    if (isActive) {
      // Active block clicked - add points
      this.sound.play('right');

      // Calculate points based on reaction time
      const currentTime = Date.now();
      const reactionTime = currentTime - block.getActivationTime();
      let points = 1;
      console.log('reactionTime', reactionTime);
      if (reactionTime < 500) points = 5;
      else if (reactionTime < 1000) points = 4;
      else if (reactionTime < 1500) points = 3;
      else if (reactionTime < 2000) points = 2;
      
      console.log('reactionTime', reactionTime);
      // Update score
      // this.score += points;
      // this.scoreText.setText(this.score.toString());
      this.scoreManager.addPoints(points, {
        startX: this.gridContainer.x + block.x,
        startY: this.gridContainer.y + block.y,
        color: '#ffff00',
        points
      });
      // this.timerManager.addTime(GameConfig.TMER_INCREASE);
      
      console.log('reactionTime', reactionTime);
      
      // Show score animation
      // this.showScoreText(this.gridContainer.x + block.x, this.gridContainer.y + block.y, `+${points}`);

      // Deactivate block and activate a new one
      block.setBlockActive(false);

      // Get block index to exclude from new activation
      const clickedBlockIndex = this.blocks.indexOf(block);
      this.activateRandomBlock(clickedBlockIndex);
      
    } else {
      // Inactive block clicked - lose points
      this.sound.play('wrong');

      // Show wrong state and penalty
      block.setBlockWrong();
      // this.score = Math.max(0, this.score - 5);
      // this.scoreText.setText(this.score.toString());

      this.scoreManager.subtractPoints(5, {
        startX: this.gridContainer.x + block.x,
        startY: this.gridContainer.y + block.y,
        color: '#ff0000',
        points: 5
      });
      this.livesManager.deductHeart(
        this.gridContainer.x + block.x,
        this.gridContainer.y + block.y
      );
      // this.timerManager.removeTime(GameConfig.TMER_PUNISHMENT);

      // this.showScoreText(this.gridContainer.x + block.x, this.gridContainer.y + block.y, '-5');
    }
    
    // Re-enable interaction after a short delay to allow the touch to complete
    this.time.delayedCall(GameConfig.COOLDOWN_DURATION * 1000, () => {
      block.setInteractive({ useHandCursor: true });
    });
  }

  private activateRandomBlock(excludeBlockIndex?: number): void {
    if (this.gameEnd) return;
    
    const currentActiveCount = this.blocks.filter(b => b.getBlockActive()).length;
    if (currentActiveCount >= GameConfig.INITIAL_ACTIVE_BLOCKS) return;

    // Get list of available positions (exclude currently active blocks and the clicked block)
    const availablePositions: number[] = [];
    
    this.blocks.forEach((block, index) => {
      const isActive = block.getBlockActive();
      const isExcluded = excludeBlockIndex !== undefined && index === excludeBlockIndex;
      
      if (!isActive && !isExcluded) {
        availablePositions.push(index);
      }
    });
    
    // If we have available positions, activate one randomly
    if (availablePositions.length > 0) {
      const randomIndex = Phaser.Utils.Array.GetRandom(availablePositions);
      this.blocks[randomIndex].setBlockActive(true);
    }
  }

  private startGame(): void {
    // Reset all blocks
    this.blocks.forEach(block => block.reset());

    // Activate initial blocks
    const positions = [
      { row: 1, col: 2 },
      { row: 2, col: 3 },
      { row: 0, col: 1 }
    ];

    for (const pos of positions) {
      const index = pos.row * 4 + pos.col;
      if (index < this.blocks.length) {
        this.blocks[index].setBlockActive(true);
      }
    }

    // Send game start event to server using proper format
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.startGame(this.gameId, this.roomId, this.playerId);
    }

    console.log('Game started with 3 active blocks');
  }

  private endGame(): void {
    if (this.gameEnd) return;

    this.gameEnd = true;
    gameActions.endGame();

    this.sound.play('timeout');

    // Immediately disable all blocks to prevent further interaction
    this.blocks.forEach(block => block.disableInteractive());

    // Notify server of game end
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.endGame(this.gameId, this.roomId, this.playerId, 'manual');
    }

    // Transition to end scene after a short delay (matching Matching Mayhem pattern)
    // this.time.delayedCall(500, () => {
    //   // Clean up the scene before transitioning
    //   this.blocks.forEach(block => block.destroy());
    //   this.blocks = [];

    //   // Stop all tweens
    //   this.tweens.killAll();

    //   console.log('Transitioning to GameEndScene with score:', this.scoreManager.getScore());
    //   this.scene.start('GameEndScene', { score: this.scoreManager.getScore() });
    // });
  }

  /**
   * Get tile ID for a block based on its position in the grid
   */
  private getTileId(block: GridBlock): string {
    const blockIndex = this.blocks.indexOf(block);
    const row = Math.floor(blockIndex / 4);
    const col = blockIndex % 4;
    return `block_${row}_${col}`;
  }

  /**
   * Handle tile tap result from server
   */
  private handleTileTapResult(data: any): void {
    console.log('Handling tile tap result:', data);

    // Update local game state based on server response
    if (data.gameState) {
      this.scoreManager.setScore(data.gameState.score);
      // Note: LivesManager doesn't have setLives, we'll handle lives through deduction events
    }

    // Update grid state if provided
    if (data.gridState) {
      this.syncGridState(data.gridState);
    }

    // Handle game end
    if (data.gameEnded) {
      this.endGame();
    }
  }

  /**
   * Sync game state with server
   */
  private syncGameState(gameState: any): void {
    if (!gameState) return;

    // Update score
    if (gameState.score !== undefined) {
      this.scoreManager.setScore(gameState.score);
    }

    // Update timer if game is active
    if (gameState.isActive && gameState.startTime) {
      // Note: TimerManager might need a method to set remaining time
      // For now, we'll just log the sync
      console.log('Syncing game timer with server state');
    }
  }

  /**
   * Sync grid state with server
   */
  private syncGridState(gridState: any): void {
    if (!gridState || !gridState.blocks) return;

    gridState.blocks.forEach((serverBlock: any) => {
      const blockIndex = serverBlock.index;
      if (blockIndex >= 0 && blockIndex < this.blocks.length) {
        this.blocks[blockIndex].setBlockActive(serverBlock.isActive);
      }
    });
  }

}
