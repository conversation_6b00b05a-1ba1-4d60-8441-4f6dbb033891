import { FingerFrenzyGame } from './FingerFrenzy';
import { BingoGame } from './Bingo';
import { MatchingMayhemGame } from './MatchingMayhem';
import { NumberSequenceGame } from './NumberSequence';

// Game factory function
export function createGame(gameId: string, containerId: string, callbacks?: any) {
	const config = {
		gameId,
		containerId,
		...callbacks
	};

	switch (gameId) {
		case 'finger-frenzy':
			return new FingerFrenzyGame(config);
		case 'bingo':
			return new BingoGame(config);
		case 'matching-mayhem':
			return new MatchingMayhemGame(config);
		case 'number-sequence':
			return new NumberSequenceGame(config);
		default:
			throw new Error(`Unknown game ID: ${gameId}`);
	}
}
