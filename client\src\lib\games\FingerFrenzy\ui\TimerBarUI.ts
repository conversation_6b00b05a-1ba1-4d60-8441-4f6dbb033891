import Phaser from 'phaser';
import type { TimerBarConfig } from '../types/types';

/**
 * ```
 * const timerBar = new TimerBarUI(scene, {
 *   width: 400,
 *   height: 35,
 *   x: centerX,
 *   y: topY
 * });
 * 
 * timerBar.create();
 * timerBar.updateProgress(0.5); // 50% progress
 * ```
 */
export default class TimerBarUI {
  private scene: Phaser.Scene;
  private config: Required<TimerBarConfig>;
  
  // UI Elements
  private container?: Phaser.GameObjects.Container;
  private timerBar?: Phaser.GameObjects.Image;
  private timerMask?: Phaser.GameObjects.Graphics;
  private timerIcon?: Phaser.GameObjects.Image;
  private leftCircle?: Phaser.GameObjects.Arc;
  private rightCircle?: Phaser.GameObjects.Arc;
  private background?: Phaser.GameObjects.Rectangle;
  
  // Gradient texture name
  private gradientTextureName: string;

  constructor(scene: Phaser.Scene, config: TimerBarConfig) {
    this.scene = scene;
    this.gradientTextureName = `timerBarGradient_${Date.now()}_${Math.random()}`;
    
    // Set default configuration
    this.config = {
      width: config.width,
      height: config.height,
      x: config.x,
      y: config.y,
      backgroundColor: config.backgroundColor ?? 0x111111,
      gradientStartColor: config.gradientStartColor ?? '#33DDFF',
      gradientEndColor: config.gradientEndColor ?? '#664DFF',
      cornerRadius: config.cornerRadius ?? 10,
      showTimerIcon: config.showTimerIcon ?? true,
      showCircularBg: config.showCircularBg ?? true,
      circleRadius: config.circleRadius ?? 30,
      circleBorderColor: config.circleBorderColor ?? 0x33DDFF,
      circleBorderWidth: config.circleBorderWidth ?? 2
    };
  }

  /**
   * Create the timer bar UI elements
   */
  public create(parentContainer?: Phaser.GameObjects.Container): void {
    // Create main container
    this.container = this.scene.add.container(0, 0);
    
    // Create background
    this.background = this.scene.add.rectangle(
      this.config.x, 
      this.config.y, 
      this.config.width, 
      this.config.height, 
      this.config.backgroundColor
    ).setOrigin(0.5);
    this.container.add(this.background);
    
    // Create gradient texture
    this.createGradientTexture();
    
    // Create timer bar with gradient
    this.timerBar = this.scene.add.image(this.config.x, this.config.y, this.gradientTextureName)
      .setOrigin(0.5);
    this.container.add(this.timerBar);
    
    // Create mask for progress
    this.createTimerMask();
    
    // Create optional circular backgrounds and icon
    if (this.config.showCircularBg) {
      this.createCircularBackgrounds();
    }
    
    if (this.config.showTimerIcon) {
      this.createTimerIcon();
    }
    
    // Add to parent container if provided
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }

  /**
   * Update the progress of the timer bar (0-1)
   */
  public updateProgress(progress: number): void {
    if (!this.timerMask || !this.timerBar) return;
    
    // Clamp progress between 0 and 1
    progress = Math.max(0, Math.min(1, progress));
    
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    const visibleWidth = barWidth * progress;
    
    // Update mask
    this.timerMask.clear();
    if (visibleWidth > 0) {
      this.timerMask.fillStyle(0xffffff, 1);
      this.timerMask.fillRoundedRect(
        this.config.x - barWidth / 2,
        this.config.y - barHeight / 2,
        visibleWidth,
        barHeight,
        this.config.cornerRadius
      );
    }
  }

  /**
   * Create the gradient texture for the timer bar
   */
  private createGradientTexture(): void {
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    
    const barTexture = this.scene.textures.createCanvas(this.gradientTextureName, barWidth, barHeight);
    const barContext = barTexture?.getContext();
    
    if (barContext && barTexture) {
      const gradient = barContext.createLinearGradient(0, 0, barWidth, 0);
      gradient.addColorStop(0, this.config.gradientStartColor);
      gradient.addColorStop(1, this.config.gradientEndColor);
      barContext.fillStyle = gradient;
      barContext.fillRect(0, 0, barWidth, barHeight);
      barTexture.refresh();
    }
  }

  /**
   * Create the mask for the timer bar progress
   */
  private createTimerMask(): void {
    if (!this.timerBar) return;
    
    const barWidth = this.config.width - 4;
    const barHeight = this.config.height - 4;
    
    this.timerMask = this.scene.make.graphics({});
    this.timerMask.fillRect(
      this.config.x - barWidth / 2,
      this.config.y - barHeight / 2,
      barWidth,
      barHeight
    );
    
    const mask = new Phaser.Display.Masks.GeometryMask(this.scene, this.timerMask);
    this.timerBar.setMask(mask);
  }

  /**
   * Create circular backgrounds on left and right
   */
  private createCircularBackgrounds(): void {
    if (!this.container) return;
    
    const leftX = this.config.x - this.config.width / 2;
    const rightX = this.config.x + this.config.width / 2;
    
    // Left circle (timer icon background)
    this.leftCircle = this.scene.add.circle(
      leftX, 
      this.config.y, 
      this.config.circleRadius, 
      0x222222
    );
    this.leftCircle.setStrokeStyle(this.config.circleBorderWidth, this.config.circleBorderColor, 1);
    this.container.add(this.leftCircle);
    
    // Right circle (timer text background)
    this.rightCircle = this.scene.add.circle(
      rightX, 
      this.config.y, 
      this.config.circleRadius, 
      0x222222
    );
    this.rightCircle.setStrokeStyle(this.config.circleBorderWidth, 0x664DFF, 1); // Purple border
    this.container.add(this.rightCircle);
  }

  /**
   * Create timer icon
   */
  private createTimerIcon(): void {
    if (!this.container) return;
    
    const leftX = this.config.x - this.config.width / 2;
    
    // Check if timer_icon texture exists, create fallback if not
    if (this.scene.textures.exists('timer_icon')) {
      this.timerIcon = this.scene.add.image(leftX, this.config.y, 'timer_icon')
        .setOrigin(0.5)
        .setScale(0.5);
    } else {
      // Create a simple clock icon as fallback
      const clockGraphics = this.scene.add.graphics();
      clockGraphics.lineStyle(2, 0x33DDFF);
      clockGraphics.strokeCircle(0, 0, 12);
      clockGraphics.moveTo(0, 0);
      clockGraphics.lineTo(0, -8);
      clockGraphics.moveTo(0, 0);
      clockGraphics.lineTo(6, 0);
      clockGraphics.setPosition(leftX, this.config.y);
      this.container.add(clockGraphics);
    }
    
    if (this.timerIcon) {
      this.container.add(this.timerIcon);
    }
  }

  /**
   * Set the gradient colors
   */
  public setGradientColors(startColor: string, endColor: string): void {
    this.config.gradientStartColor = startColor;
    this.config.gradientEndColor = endColor;
    
    // Recreate gradient texture
    if (this.scene.textures.exists(this.gradientTextureName)) {
      this.scene.textures.remove(this.gradientTextureName);
    }
    this.createGradientTexture();
    
    // Update timer bar texture
    if (this.timerBar) {
      this.timerBar.setTexture(this.gradientTextureName);
    }
  }

  /**
   * Get the container for additional positioning/effects
   */
  public getContainer(): Phaser.GameObjects.Container | undefined {
    return this.container;
  }

  /**
   * Get the right circle position for timer text placement
   */
  public getRightCirclePosition(): { x: number; y: number } {
    return {
      x: this.config.x + this.config.width / 2,
      y: this.config.y
    };
  }

  /**
   * Clean up resources
   */
  public destroy(): void {
    // Remove gradient texture
    if (this.scene.textures.exists(this.gradientTextureName)) {
      this.scene.textures.remove(this.gradientTextureName);
    }
    
    if (this.container) {
      this.container.destroy();
    }
    
    this.timerBar = undefined;
    this.timerMask = undefined;
    this.timerIcon = undefined;
    this.leftCircle = undefined;
    this.rightCircle = undefined;
    this.background = undefined;
    this.container = undefined;
  }
}
