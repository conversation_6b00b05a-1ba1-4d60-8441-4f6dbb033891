{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "tsx watch src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "socket.io": "^4.8.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.1.0", "@types/socket.io": "^3.0.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}}