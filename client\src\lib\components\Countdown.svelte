<script lang="ts">
  import Icon from "@iconify/svelte";

  interface Props {
    duration: number;
    show: boolean;
  }

  let { duration = 3, show = false }: Props = $props();

  let currentCount = $state(duration);
  let intervalId: number | null = $state(null);

  $effect(() => {
    if (show) {
      console.log("Starting countdown");
      startCountdown();
    }

    return cleanup;
  });

  function startCountdown() {
    currentCount = duration;
    intervalId = setInterval(() => {
      currentCount--;
      if (currentCount < 0) {
        clearInterval(intervalId!);
      }
    }, 1300);
  }

  function cleanup() {
    if (intervalId) {
      clearInterval(intervalId);
      intervalId = null;
    }
  }
</script>

{#if show && currentCount >= 0}
  <!-- Countdown Overlay -->
  <div
    class="fixed inset-0 bg-black/60 flex justify-center items-center z-[2000]"
  >
    <img
      class="counter"
      src="/assets/images/counter/{currentCount === 0
        ? 'GO'
        : currentCount}.svg"
      alt=""
    />

    <Icon icon="tabler:number-3" />
  </div>
{/if}

<style>
  .counter {
    width: 40vh;
    height: auto;

    animation: tweenAnimation 1.3s ease-in-out infinite;
    transform: scale(0.5);
    opacity: 0;
  }

  @keyframes tweenAnimation {
    0% {
      transform: scale(0.2);
      opacity: 0;
    }
    20% {
      transform: scale(1.5);
      opacity: 1;
    }
    80% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1);
      /* transform: scale(0.2); */
      opacity: 0;
    }
  }
</style>
