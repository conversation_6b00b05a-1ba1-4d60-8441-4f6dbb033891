export interface PlayerProfile {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
  stats: PlayerStats;
  preferences: PlayerPreferences;
  createdAt: number;
  lastActive: number;
}

export interface PlayerStats {
  gamesPlayed: number;
  gamesWon: number;
  totalScore: number;
  averageScore: number;
  bestScore: number;
  gameStats: Record<string, GameSpecificStats>;
}

export interface GameSpecificStats {
  played: number;
  won: number;
  bestScore: number;
  averageScore: number;
  totalTime: number;
}

export interface PlayerPreferences {
  soundEnabled: boolean;
  musicEnabled: boolean;
  notifications: boolean;
  theme: 'light' | 'dark' | 'auto';
  language: string;
}

export interface PlayerSession {
  playerId: string;
  socketId: string;
  roomId?: string;
  authToken?: string;
  connectedAt: number;
  lastActivity: number;
}
