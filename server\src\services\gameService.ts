import { GameType, GameState, GameAction, GameResults } from '../types/game';
import { logger } from '../utils/logger';
import { DEFAULT_GAME_DURATION } from '../utils/constants';

export class GameService {
  private gameStates: Map<string, GameState> = new Map();
  private gameTimers: Map<string, NodeJS.Timeout> = new Map();

  createGameState(roomId: string, gameType: GameType, player: string, initialLives: number): GameState {
    const gameState: GameState = {
      gameType,
      status: 'waiting',
      player,
      score: 0,
      lives: initialLives
    };

    this.gameStates.set(roomId, gameState);
    logger.info(`Game state created for room ${roomId}, game type: ${gameType}${initialLives ? `, lives: ${initialLives}` : ''}`);
    return gameState;
  }

  getGameState(roomId: string): GameState | undefined {
    return this.gameStates.get(roomId);
  }

  startGame(roomId: string): boolean {
    const gameState = this.gameStates.get(roomId);
    if (!gameState) {
      logger.warn(`Cannot start game: no game state for room ${roomId}`);
      return false;
    }

    if (gameState.status !== 'waiting') {
      logger.warn(`Cannot start game: game in room ${roomId} is not in waiting state`);
      return false;
    }

    gameState.status = 'active';
    gameState.startTime = Date.now();
    
    // Set up game timer
    const duration = DEFAULT_GAME_DURATION[gameState.gameType];
    const timer = setTimeout(() => {
      this.endGame(roomId, 'timeout');
    }, duration);

    this.gameTimers.set(roomId, timer);
    
    logger.info(`Game started in room ${roomId}, duration: ${duration}ms`);
    return true;
  }

  endGame(roomId: string, reason: 'completed' | 'timeout' | 'disconnection' | 'forfeit'): GameResults | null {
    const gameState = this.gameStates.get(roomId);
    if (!gameState) {
      logger.warn(`Cannot end game: no game state for room ${roomId}`);
      return null;
    }

    // Clear timer if exists
    const timer = this.gameTimers.get(roomId);
    if (timer) {
      clearTimeout(timer);
      this.gameTimers.delete(roomId);
    }

    gameState.status = 'ended';

    const results: GameResults = {
      gameType: gameState.gameType,
      score: gameState.score,
      endReason: reason,
    };

    logger.info(`Game ended in room ${roomId}, reason: ${reason}`);
    return results;
  }

  updateScore(roomId: string, playerId: string, score: number): boolean {
    const gameState = this.gameStates.get(roomId);
    if (!gameState) {
      logger.warn(`Cannot update score: no game state for room ${roomId}`);
      return false;
    }

    if (gameState.status !== 'active') {
      logger.warn(`Cannot update score: game in room ${roomId} is not active`);
      return false;
    }

    if (gameState.player !== playerId) {
      logger.warn(`Cannot update score: player ${playerId} not in game ${roomId}`);
      return false;
    }

    gameState.score = score;
    logger.info(`Score updated for player ${playerId} in room ${roomId}: ${score}`);
    return true;
  }

  updateLives(roomId: string, playerId: string, lives: number): boolean {
    const gameState = this.gameStates.get(roomId);
    if (!gameState) {
      logger.warn(`Cannot update lives: no game state for room ${roomId}`);
      return false;
    }

    if (gameState.status !== 'active') {
      logger.warn(`Cannot update lives: game in room ${roomId} is not active`);
      return false;
    }

    if (gameState.player !== playerId) {
      logger.warn(`Cannot update lives: player ${playerId} not in game ${roomId}`);
      return false;
    }

    gameState.lives = lives;
    logger.info(`Lives updated for player ${playerId} in room ${roomId}: ${lives}`);
    return true;
  }

  deductLife(roomId: string, playerId: string): { newLives: number; gameEnded: boolean } {
    const gameState = this.gameStates.get(roomId);
    if (!gameState || gameState.lives === undefined) {
      return { newLives: 0, gameEnded: false };
    }

    const newLives = Math.max(0, gameState.lives - 1);
    this.updateLives(roomId, playerId, newLives);

    const gameEnded = newLives <= 0;
    if (gameEnded) {
      this.endGame(roomId, 'completed'); // or 'dead' if we add that to the type
    }

    return { newLives, gameEnded };
  }

  addLife(roomId: string, playerId: string): number {
    const gameState = this.gameStates.get(roomId);
    if (!gameState || gameState.lives === undefined) {
      return 0;
    }

    const newLives = gameState.lives + 1;
    this.updateLives(roomId, playerId, newLives);
    return newLives;
  }

  getLives(roomId: string): number {
    const gameState = this.gameStates.get(roomId);
    return gameState?.lives || 0;
  }

  processGameAction(roomId: string, playerId: string, action: GameAction): boolean {
    const gameState = this.gameStates.get(roomId);
    if (!gameState) {
      logger.warn(`Cannot process action: no game state for room ${roomId}`);
      return false;
    }

    if (gameState.status !== 'active') {
      logger.warn(`Cannot process action: game in room ${roomId} is not active`);
      return false;
    }

    if (gameState.player !== playerId) {
      logger.warn(`Cannot process action: player ${playerId} not in game ${roomId}`);
      return false;
    }

    // Basic action validation (extend based on game type)
    if (!this.validateAction(gameState.gameType, action)) {
      logger.warn(`Invalid action from player ${playerId} in room ${roomId}:`, action);
      return false;
    }

    logger.debug(`Action processed for player ${playerId} in room ${roomId}:`, action.type);
    return true;
  }

  deleteGameState(roomId: string): boolean {
    const timer = this.gameTimers.get(roomId);
    if (timer) {
      clearTimeout(timer);
      this.gameTimers.delete(roomId);
    }

    const deleted = this.gameStates.delete(roomId);
    if (deleted) {
      logger.info(`Game state deleted for room ${roomId}`);
    }
    return deleted;
  }

  // Timer management methods
  setGameTimer(roomId: string, duration: number, callback: () => void): boolean {
    // Clear existing timer if any
    this.clearGameTimer(roomId);

    const timer = setTimeout(() => {
      callback();
      this.gameTimers.delete(roomId);
    }, duration);

    this.gameTimers.set(roomId, timer);
    logger.info(`Game timer set for room ${roomId}, duration: ${duration}ms`);
    return true;
  }

  clearGameTimer(roomId: string): void {
    const timer = this.gameTimers.get(roomId);
    if (timer) {
      clearTimeout(timer);
      this.gameTimers.delete(roomId);
      logger.info(`Game timer cleared for room ${roomId}`);
    }
  }

  hasActiveTimer(roomId: string): boolean {
    return this.gameTimers.has(roomId);
  }

  private validateAction(gameType: GameType, action: GameAction): boolean {
    // Basic validation - extend based on specific game requirements
    if (!action.type || typeof action.timestamp !== 'number') {
      return false;
    }

    // Game-specific validation can be added here
    switch (gameType) {
      case 'finger-frenzy':
        return ['tap', 'combo'].includes(action.type);
      case 'bingo':
        return ['mark_number', 'call_bingo'].includes(action.type);
      case 'matching-mayhem':
        return ['flip_card', 'match_found'].includes(action.type);
      case 'number-sequence':
        return ['select_number', 'submit_sequence'].includes(action.type);
      default:
        return true;
    }
  }
}

export const gameService = new GameService();
export default gameService;
